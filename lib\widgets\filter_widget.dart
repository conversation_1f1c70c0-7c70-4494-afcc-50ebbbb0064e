import 'package:flutter/material.dart';
import '../controllers/filter_controller.dart' as filter_ctrl;
import 'package:cat_tv/models/region.dart';
import 'package:cat_tv/models/country.dart';
import 'package:cat_tv/models/language.dart';
import 'package:cat_tv/models/category.dart' as cat_model;

class FilterWidget extends StatelessWidget {
  final filter_ctrl.FilterController controller;
  final List<Region> regions;
  final List<Country> countries;
  final List<Language> languages;
  final List<cat_model.Category> categories;

  const FilterWidget({
    super.key,
    required this.controller,
    required this.regions,
    required this.countries,
    required this.languages,
    required this.categories,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    DropdownButton<int?>(
                      value: controller.filter.categoryId,
                      hint: const Text('Category'),
                      items: [
                        const DropdownMenuItem(
                          value: null,
                          child: Text('All Categories'),
                        ),
                        ...categories.map(
                          (cat) => DropdownMenuItem(
                            value: cat.id,
                            child: Text(cat.name),
                          ),
                        ),
                      ],
                      onChanged: (val) {
                        if (val == null) {
                          controller.clearFilter(category: true);
                        } else {
                          controller.setFilter(categoryId: val);
                        }
                      },
                    ),
                    const SizedBox(width: 16),
                    DropdownButton<String?>(
                      value: controller.filter.region,
                      hint: const Text('Region'),
                      items: [
                        const DropdownMenuItem(
                          value: null,
                          child: Text('All Regions'),
                        ),
                        ...regions.map(
                          (r) => DropdownMenuItem(
                            value: r.code,
                            child: Text(r.name),
                          ),
                        ),
                      ],
                      onChanged: (val) {
                        if (val == null) {
                          controller.clearFilter(region: true);
                        } else {
                          controller.setFilter(
                            region: val,
                            country: null,
                          ); // Reset country on region change
                        }
                      },
                    ),
                    const SizedBox(width: 16),
                    DropdownButton<String?>(
                      value: controller.filter.country,
                      hint: const Text('Country'),
                      items: [
                        const DropdownMenuItem(
                          value: null,
                          child: Text('All Countries'),
                        ),
                        ...countries.map(
                          (c) => DropdownMenuItem(
                            value: c.code,
                            child: Row(
                              children: [
                                if (c.flag.isNotEmpty) Text(c.flag),
                                if (c.flag.isNotEmpty) const SizedBox(width: 4),
                                Text(c.name),
                              ],
                            ),
                          ),
                        ),
                      ],
                      onChanged: (val) {
                        if (val == null) {
                          controller.clearFilter(country: true);
                        } else {
                          controller.setFilter(country: val);
                        }
                      },
                    ),
                    const SizedBox(width: 16),
                    DropdownButton<String?>(
                      value: controller.filter.language,
                      hint: const Text('Language'),
                      items: [
                        const DropdownMenuItem(
                          value: null,
                          child: Text('All Languages'),
                        ),
                        ...languages.map(
                          (l) => DropdownMenuItem(
                            value: l.code,
                            child: Text(l.name),
                          ),
                        ),
                      ],
                      onChanged: (val) {
                        if (val == null) {
                          controller.clearFilter(language: true);
                        } else {
                          controller.setFilter(language: val);
                        }
                      },
                    ),
                    const SizedBox(width: 16),
                    ElevatedButton.icon(
                      onPressed: () => controller.resetAll(),
                      icon: const Icon(Icons.refresh),
                      label: const Text('Reset'),
                      style: ElevatedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        backgroundColor: Colors.blueGrey.shade50,
                        foregroundColor: Colors.blueGrey.shade900,
                        elevation: 0,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
