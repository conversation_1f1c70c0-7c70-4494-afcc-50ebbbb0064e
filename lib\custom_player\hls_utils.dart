import 'package:flutter/foundation.dart';
import 'package:flutter_hls_parser/flutter_hls_parser.dart';
import 'package:http/http.dart' as http;

Future<List<Variant>> getHLSVariants(String url) async {
  try {
    final response = await http.get(Uri.parse(url));
    if (response.statusCode == 200) {
      final playlist = await HlsPlaylistParser.create().parseString(
        Uri.parse(url),
        response.body,
      );

      if (playlist is HlsMasterPlaylist) {
        return playlist.variants.cast<Variant>();
      }
    } else {
      if (kDebugMode) {
        print('Failed to load HLS playlist: ${response.statusCode}');
      }
    }
  } catch (e) {
    if (kDebugMode) {
      print('Error parsing HLS playlist: $e');
    }
  }
  return [];
}
