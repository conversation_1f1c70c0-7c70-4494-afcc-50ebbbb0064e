import 'package:flutter/foundation.dart';
import '../models/channel_filter.dart';

class FilterController extends ChangeNotifier {
  ChannelFilter _filter = const ChannelFilter();

  ChannelFilter get filter => _filter;

  void setFilter({
    String? region,
    String? country,
    String? language,
    String? search,
    int? categoryId,
  }) {
    _filter = ChannelFilter(
      region: region ?? _filter.region,
      country: country ?? _filter.country,
      language: language ?? _filter.language,
      search: search ?? _filter.search,
      categoryId: categoryId ?? _filter.categoryId,
    );
    notifyListeners();
  }

  void clearFilter({
    bool region = false,
    bool country = false,
    bool language = false,
    bool search = false,
    bool category = false,
  }) {
    _filter = ChannelFilter(
      region: region ? null : _filter.region,
      country: country ? null : _filter.country,
      language: language ? null : _filter.language,
      search: search ? null : _filter.search,
      categoryId: category ? null : _filter.categoryId,
    );
    notifyListeners();
  }

  void resetAll() {
    _filter = const ChannelFilter();
    notifyListeners();
  }
}
