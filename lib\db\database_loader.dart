import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart' as p;
import 'package:flutter/services.dart';
import 'dart:io';

class DatabaseLoader {
  // load database from assets located in assets/db
  static Future<Database> openPrebuiltDatabase() async {
    final dbPath = await getDatabasesPath();
    final dbFile = File(p.join(dbPath, 'app.db'));
    if (kDebugMode) {
      print('Database path: ${dbFile.path}');
    }

    // Ensure the parent directory exists
    final parentDir = dbFile.parent;
    if (!await parentDir.exists()) {
      await parentDir.create(recursive: true);
    }

    if (!await dbFile.exists()) {
      // Copy the prebuilt database from assets
      final byteData = await rootBundle.load('assets/db/app.db');
      final buffer = byteData.buffer.asUint8List();
      await dbFile.writeAsBytes(buffer, flush: true);
    }

    return openDatabase(dbFile.path);
  }
}
