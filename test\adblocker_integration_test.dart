import 'package:flutter_test/flutter_test.dart';
import 'package:adblocker_webview/adblocker_webview.dart';

void main() {
  group('AdBlocker WebView Integration Tests', () {
    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    test('AdBlocker controller should initialize successfully', () async {
      // Test that the AdBlocker controller can be initialized
      await AdBlockerWebviewController.instance.initialize(
        FilterConfig(filterTypes: [FilterType.easyList, FilterType.adGuard]),
        [],
      );

      // If we get here without throwing, the initialization was successful
      expect(true, isTrue);
    });

    test('FilterConfig should be created with correct filter types', () {
      final config = FilterConfig(
        filterTypes: [FilterType.easyList, FilterType.adGuard],
      );

      expect(config.filterTypes, contains(FilterType.easyList));
      expect(config.filterTypes, contains(FilterType.adGuard));
      expect(config.filterTypes.length, equals(2));
    });

    test('AdBlocker controller should be a singleton', () {
      final instance1 = AdBlockerWebviewController.instance;
      final instance2 = AdBlockerWebviewController.instance;

      expect(instance1, same(instance2));
    });
  });
}
