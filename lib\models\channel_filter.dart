class ChannelFilter {
  final String? region;
  final String? country;
  final String? language;
  final String? search;
  final int? categoryId;

  const ChannelFilter({
    this.region,
    this.country,
    this.language,
    this.search,
    this.categoryId,
  });

  ChannelFilter copyWith({
    String? region,
    String? country,
    String? language,
    String? search,
    int? categoryId,
  }) {
    return ChannelFilter(
      region: region ?? this.region,
      country: country ?? this.country,
      language: language ?? this.language,
      search: search ?? this.search,
      categoryId: categoryId ?? this.categoryId,
    );
  }

  static const empty = ChannelFilter();
}
